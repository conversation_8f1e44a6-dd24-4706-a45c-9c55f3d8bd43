import asyncio
import logging
import subprocess
import signal
import psutil
import os
from aiohttp import web

import config
from utils import get_local_ip, log_process_output, log_mediamtx_output, ffmpeg_connected_to_mediamtx
from onvif_service import handle_onvif, handle_snapshot, start_onvif_discovery
from http_handlers import handle_start, handle_start_twoway, handle_stop, handle_doorbell_press_with_image, handle_update_snapshot, handle_audio_input
from media_pipeline import stream_generator, audio_stream_generator


async def start_ffmpeg(use_software_encoder=False):
    """Start or restart the FFmpeg process"""
    # Use the audio FIFO path from config (created in main())
    audio_fifo = config.audio_fifo_path
    
    if use_software_encoder:
        logging.warning("Using software encoder (libx264) - VAAPI may have failed")
        command = [
            'ffmpeg', '-y',
            '-stats',
            # CRITICAL: Buffer overflow prevention - remove nobuffer flag
            # '-fflags', 'nobuffer',  # REMOVED: This disables flow control
            '-probesize', '32',
            '-analyzeduration', '0',
            # CRITICAL: Add real-time processing to prevent buffer buildup
            '-re',  # Process input at native frame rate
            # Video input from pipe:0 (stdin)
            '-f', 'rawvideo',
            '-pix_fmt', 'bgr24',
            '-s', f'{config.VIDEO_WIDTH}x{config.VIDEO_HEIGHT}',
            '-r', str(config.DUMMY_FPS),
            # CRITICAL: Reduce thread queue size to prevent memory explosion
            '-thread_queue_size', '64',  # Reduced from 512 to prevent buffer overflow
            '-i', 'pipe:0',
            # Audio input from FIFO (live audio) or fallback to dummy
            '-f', 's16le',
            '-ar', '8000',
            '-ac', '1',
            '-thread_queue_size', '64',  # Reduced from 512
            '-i', audio_fifo,
            # Video encoding with SOFTWARE encoder - optimized for memory
            '-c:v', 'libx264',
            '-preset', 'superfast',  # Changed from ultrafast for better efficiency
            '-tune', 'zerolatency',
            '-b:v', '1000k',
            '-maxrate', '1000k',
            '-bufsize', '500k',  # CRITICAL: Limit encoder buffer size
            '-g', '24',
            '-threads', '2',  # CRITICAL: Limit threads to reduce memory overhead
            # Audio encoding
            '-c:a', 'aac',
            '-ar', '16000',
            '-b:a', '32k',
            # Map inputs
            '-map', '0:v:0',
            '-map', '1:a:0',
            # CRITICAL: Add buffer size limits for output
            '-rtbufsize', '100M',  # Limit real-time buffer to 100MB
            '-f', 'rtsp',
            config.MEDIAMTX_URL
        ]
    else:
        command = [
            'ffmpeg', '-y',
            '-stats',
            # CRITICAL: Buffer overflow prevention - remove nobuffer flag
            # '-fflags', 'nobuffer',  # REMOVED: This disables flow control
            '-probesize', '32',
            '-analyzeduration', '0',
            # CRITICAL: Add real-time processing to prevent buffer buildup
            '-re',  # Process input at native frame rate
            '-vaapi_device', '/dev/dri/renderD128',
            # Video input from pipe:0 (stdin)
            '-f', 'rawvideo',
            '-pix_fmt', 'bgr24',
            '-s', f'{config.VIDEO_WIDTH}x{config.VIDEO_HEIGHT}',
            '-r', str(config.DUMMY_FPS),
            # CRITICAL: Reduce thread queue size to prevent memory explosion
            '-thread_queue_size', '64',  # Reduced from 512 to prevent buffer overflow
            '-i', 'pipe:0',
            # Audio input from FIFO (live audio) or fallback to dummy
            '-f', 's16le',
            '-ar', '8000',
            '-ac', '1',
            '-thread_queue_size', '64',  # Reduced from 512
            '-i', audio_fifo,
            # Video encoding with HARDWARE encoder - optimized for memory
            '-c:v', 'h264_vaapi',
            '-vf', 'format=nv12,hwupload',
            '-preset', 'ultrafast',
            '-tune', 'zerolatency',
            '-b:v', '1000k',
            '-maxrate', '1000k',
            '-bufsize', '500k',  # CRITICAL: Limit encoder buffer size
            '-g', '24',
            # Audio encoding
            '-c:a', 'aac',
            '-ar', '16000',
            '-b:a', '32k',
            # Map inputs
            '-map', '0:v:0',
            '-map', '1:a:0',
            # CRITICAL: Add buffer size limits for output
            '-rtbufsize', '100M',  # Limit real-time buffer to 100MB
            '-f', 'rtsp',
            config.MEDIAMTX_URL
        ]

    encoder_type = "software (libx264)" if use_software_encoder else "hardware (VAAPI)"
    logging.info(f"Starting FFmpeg process with {encoder_type}: {' '.join(command)}")
    
    # CRITICAL: Check if MediaMTX is ready to receive streams
    if config.mediamtx_process is None or config.mediamtx_process.returncode is not None:
        logging.critical("🔥 CANNOT START FFMPEG: MediaMTX process is not running!")
        return False
    
    # Wait a moment for MediaMTX to be fully initialized
    await asyncio.sleep(0.5)
    
    try:
        config.ffmpeg_process = await asyncio.create_subprocess_exec(*command, stdin=subprocess.PIPE, stderr=subprocess.PIPE)
        asyncio.create_task(log_process_output(config.ffmpeg_process.stderr, logging.error, prefix="[FFMPEG] "))
        logging.info(f"✅ FFmpeg process started with PID: {config.ffmpeg_process.pid}")
        
        # Give FFmpeg a moment to attempt connection to MediaMTX
        await asyncio.sleep(1)
        
        # Check if FFmpeg is still running after connection attempt
        if config.ffmpeg_process.returncode is not None:
            logging.critical(f"🔥 FFMPEG DIED IMMEDIATELY: return code {config.ffmpeg_process.returncode}")
            logging.critical("This suggests MediaMTX /doorbell path is not ready or connection failed")
            return False
            
        return True
    except Exception as e:
        logging.critical(f"🔥 FAILED TO START FFMPEG: {e}")
        return False


async def ffmpeg_monitor():
    """Monitor FFmpeg process and restart if needed"""
    while True:
        try:
            if config.ffmpeg_process is None or config.ffmpeg_process.returncode is not None:
                if config.ffmpeg_process and config.ffmpeg_process.returncode is not None:
                    logging.error(f"FFmpeg process died with return code: {config.ffmpeg_process.returncode}")
                    # Log additional context about why FFmpeg died
                    if config.ffmpeg_process.returncode == -9:
                        logging.error("FFmpeg killed by SIGKILL - likely OOM killer or system memory pressure")
                    elif config.ffmpeg_process.returncode == -15:
                        logging.error("FFmpeg killed by SIGTERM - likely terminated by system or our code")
                    elif config.ffmpeg_process.returncode == 1:
                        logging.error("FFmpeg exited with error - likely input/output/codec issues")
                    else:
                        logging.error(f"FFmpeg exited with unknown return code: {config.ffmpeg_process.returncode}")
                else:
                    logging.warning("FFmpeg process died. Attempting to restart...")
                # Clear live flag to prevent issues during restart
                config.is_live.clear()
                config.is_audio_live.clear()
                
                # CRITICAL: Clear all queues aggressively to prevent memory leak
                logging.critical("FFmpeg died - clearing all queues to prevent memory leak")
                try:
                    # Clear video queue
                    video_frames_cleared = 0
                    while not config.live_frame_queue.empty():
                        config.live_frame_queue.get_nowait()
                        config.live_frame_queue.task_done()
                        video_frames_cleared += 1
                    
                    # Clear audio queues
                    audio_frames_cleared = 0
                    while not config.live_audio_queue.empty():
                        config.live_audio_queue.get_nowait()
                        config.live_audio_queue.task_done()
                        audio_frames_cleared += 1
                    
                    logging.critical(f"Cleared queues: {video_frames_cleared} video, {audio_frames_cleared} audio frames")
                    
                    # Force garbage collection to free memory immediately
                    import gc
                    collected = gc.collect()
                    logging.critical(f"Forced GC after FFmpeg death: collected {collected} objects")

                    # Signal memory monitor to reset baseline after restart
                    config.reset_memory_baseline = True

                except Exception as e:
                    logging.error(f"Error clearing queues after FFmpeg death: {e}")

                # Wait a moment before restart
                await asyncio.sleep(2)
                
                # Use software encoder based on config flag
                use_software = not config.ENABLE_HARDWARE_ACCELERATION
                if not await start_ffmpeg(use_software_encoder=use_software):
                    if config.ENABLE_HARDWARE_ACCELERATION:
                        logging.error("Failed to restart FFmpeg with hardware encoder, trying software encoder...")
                        await asyncio.sleep(1)
                        if not await start_ffmpeg(use_software_encoder=True):
                            logging.error("Failed to restart FFmpeg with both hardware and software encoders. Waiting before retry...")
                            await asyncio.sleep(5)
                            continue
                    else:
                        logging.error("Failed to restart FFmpeg with software encoder. Waiting before retry...")
                        await asyncio.sleep(5)
                        continue
            else:
                # Check if process is actually responsive
                try:
                    # For asyncio subprocess, just check if returncode is still None
                    if config.ffmpeg_process.returncode is not None:
                        logging.error(f"FFmpeg process died with return code: {config.ffmpeg_process.returncode}")
                        continue  # This will restart FFmpeg in the next iteration
                    else:
                        # Additional check: verify process is actually alive using psutil
                        import psutil
                        try:
                            process = psutil.Process(config.ffmpeg_process.pid)
                            if not process.is_running():
                                logging.error(f"FFmpeg process PID {config.ffmpeg_process.pid} is not running (zombie/dead)")
                                config.ffmpeg_process = None
                                continue
                            elif process.status() == psutil.STATUS_ZOMBIE:
                                logging.error(f"FFmpeg process PID {config.ffmpeg_process.pid} is in zombie state")
                                config.ffmpeg_process = None
                                continue
                            else:
                                # Additional health checks for unresponsive FFmpeg
                                cpu_percent = process.cpu_percent(interval=None)
                                memory_mb = process.memory_info().rss / (1024 * 1024)
                                num_threads = process.num_threads()
                                
                                # Log FFmpeg health every 30 seconds
                                import time
                                current_time = time.time()
                                if not hasattr(config, '_last_ffmpeg_health_log'):
                                    config._last_ffmpeg_health_log = 0
                                if current_time - config._last_ffmpeg_health_log >= 30:
                                    logging.info(f"[FFMPEG HEALTH] PID: {config.ffmpeg_process.pid}, CPU: {cpu_percent:.1f}%, Memory: {memory_mb:.1f}MB, Threads: {num_threads}")
                                    config._last_ffmpeg_health_log = current_time
                        except psutil.NoSuchProcess:
                            logging.error(f"FFmpeg process PID {config.ffmpeg_process.pid} no longer exists")
                            config.ffmpeg_process = None
                            continue
                        except Exception as e:
                            logging.error(f"Error checking FFmpeg process with psutil: {e}")
                            # Don't force restart on psutil errors, just continue
                            pass
                except Exception as e:
                    logging.error(f"Error checking FFmpeg process: {e}")
                    # Force restart
                    config.ffmpeg_process = None
                    continue
            await asyncio.sleep(2)  # Check every 2 seconds for faster FFmpeg death detection
        except asyncio.CancelledError:
            logging.info("FFmpeg monitor cancelled")
            break
        except Exception as e:
            logging.error(f"Error in FFmpeg monitor: {e}")
            await asyncio.sleep(5)


async def memory_monitor():
    """Monitor memory usage with early warning system and emergency shutdown"""
    process = psutil.Process(os.getpid())
    last_memory_mb = 0
    baseline_memory = None
    memory_growth_warnings = 0  # Track consecutive warnings
    baseline_reset_count = 0  # Track how many times we reset baseline
    last_growth_value = None  # Track if growth value is stuck
    stuck_growth_count = 0  # Count how many times we see the same growth value

    while True:
        try:
            # Get current memory usage
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024  # Convert to MB

            # Establish baseline on first run or after FFmpeg restarts
            if baseline_memory is None or getattr(config, 'reset_memory_baseline', False):
                if getattr(config, 'reset_memory_baseline', False):
                    logging.info("Resetting memory baseline due to FFmpeg restart")
                    config.reset_memory_baseline = False
                baseline_memory = memory_mb
                baseline_reset_count += 1
                logging.info(f"Memory baseline established: {baseline_memory:.1f}MB (reset #{baseline_reset_count})")
                last_memory_mb = memory_mb  # Initialize last_memory_mb
                memory_growth_warnings = 0  # Reset warnings on baseline reset
                await asyncio.sleep(3)
                continue

            memory_growth = memory_mb - baseline_memory
            memory_change = memory_mb - last_memory_mb

            # Debug logging for troubleshooting (reduced frequency for production)
            if abs(memory_change) > 10:  # Log significant changes (increased threshold)
                logging.debug(f"Memory debug: current={memory_mb:.1f}MB, baseline={baseline_memory:.1f}MB, growth={memory_growth:.1f}MB, change={memory_change:+.1f}MB")

            # Detect if memory growth calculation is stuck (indicating a bug)
            # Only trigger if we have significant growth that's stuck, not stable low growth
            if last_growth_value is not None and abs(memory_growth - last_growth_value) < 0.1:
                # Only count as "stuck" if growth is significant (>50MB) - stable low growth is normal
                if abs(memory_growth) > 50:
                    stuck_growth_count += 1
                    if stuck_growth_count >= 10:  # Same significant growth value for 30+ seconds
                        logging.critical(f"MEMORY MONITORING BUG DETECTED: Growth stuck at {memory_growth:.1f}MB for {stuck_growth_count * 3} seconds")
                        logging.critical(f"Debug: current={memory_mb:.1f}MB, baseline={baseline_memory:.1f}MB, last_memory={last_memory_mb:.1f}MB")
                        logging.critical("Resetting baseline to fix stuck monitoring...")
                        baseline_memory = memory_mb
                        last_memory_mb = memory_mb
                        memory_growth_warnings = 0
                        stuck_growth_count = 0
                        await asyncio.sleep(3)
                        continue
                else:
                    # Reset counter for stable/low growth - this is normal behavior
                    stuck_growth_count = 0
            else:
                stuck_growth_count = 0
            last_growth_value = memory_growth

            # EMERGENCY SHUTDOWN if memory limit exceeded
            if memory_mb > config.MEMORY_LIMIT_MB:
                logging.critical(f"MEMORY LIMIT EXCEEDED: {memory_mb:.1f}MB > {config.MEMORY_LIMIT_MB}MB - EMERGENCY SHUTDOWN!")

                # Cancel any active WebRTC sessions immediately
                if config.current_stream_task:
                    logging.critical("Cancelling active WebRTC session...")
                    config.current_stream_task.cancel()
                    config.current_stream_task = None
                
                # Clear all events and flags
                config.is_live.clear()
                config.is_audio_live.clear()
                
                # Clear all queues aggressively
                try:
                    # Clear video queue
                    while not config.live_frame_queue.empty():
                        config.live_frame_queue.get_nowait()
                        config.live_frame_queue.task_done()
                    
                    # Clear audio queues
                    while not config.live_audio_queue.empty():
                        config.live_audio_queue.get_nowait()
                        config.live_audio_queue.task_done()
                    
                    # Clear event queue
                    while not config.g_event_queue.empty():
                        config.g_event_queue.get_nowait()
                        
                    logging.critical("All queues cleared")
                except Exception as e:
                    logging.critical(f"Error clearing queues: {e}")
                
                # Force multiple garbage collections
                import gc
                for i in range(3):
                    collected = gc.collect()
                    logging.critical(f"GC run {i+1}: collected {collected} objects")
                
                # Terminate processes forcefully
                if config.ffmpeg_process:
                    logging.critical("Terminating FFmpeg process...")
                    config.ffmpeg_process.terminate()
                if config.mediamtx_process:
                    logging.critical("Terminating MediaMTX process...")
                    config.mediamtx_process.terminate()
                
                # Exit immediately
                logging.critical("=" * 80)
                logging.critical("EMERGENCY SHUTDOWN TRIGGERED - MEMORY LIMIT EXCEEDED")
                logging.critical("Application exiting to prevent OOM kill")
                logging.critical("=" * 80)
                os._exit(1)
            
            # CIRCUIT BREAKER: Aggressive early intervention
            elif memory_growth > 300:  # 300MB+ growth from baseline
                memory_growth_warnings += 1
                logging.critical(f"CIRCUIT BREAKER: Memory growth {memory_growth:.1f}MB (warning #{memory_growth_warnings}) - triggering aggressive cleanup")
                logging.critical(f"Circuit breaker debug: current={memory_mb:.1f}MB, baseline={baseline_memory:.1f}MB, calculated_growth={memory_growth:.1f}MB")

                # Cancel WebRTC session to stop data flow
                if config.current_stream_task:
                    logging.critical("Circuit breaker: Cancelling WebRTC session to stop memory leak")
                    config.current_stream_task.cancel()
                    config.current_stream_task = None
                
                # Clear all queues immediately
                try:
                    video_cleared = 0
                    while not config.live_frame_queue.empty():
                        config.live_frame_queue.get_nowait()
                        config.live_frame_queue.task_done()
                        video_cleared += 1
                    
                    audio_cleared = 0
                    while not config.live_audio_queue.empty():
                        config.live_audio_queue.get_nowait()
                        config.live_audio_queue.task_done()
                        audio_cleared += 1
                    
                    logging.critical(f"Circuit breaker: Cleared {video_cleared} video, {audio_cleared} audio frames")
                except Exception as e:
                    logging.critical(f"Circuit breaker queue clear error: {e}")
                
                # Force garbage collection
                import gc
                collected = gc.collect()
                logging.critical(f"Circuit breaker: GC collected {collected} objects")
                
                # If multiple warnings, restart FFmpeg to clear system buffers
                if memory_growth_warnings >= 3:
                    logging.critical("Circuit breaker: Multiple warnings - restarting FFmpeg to clear system buffers")
                    if config.ffmpeg_process:
                        config.ffmpeg_process.terminate()
                        config.ffmpeg_process = None
                    memory_growth_warnings = 0  # Reset counter after restart
                    # Reset baseline after FFmpeg restart to get accurate measurements
                    baseline_memory = None
                    logging.info("Circuit breaker: Baseline will be reset after FFmpeg restart")
                
            # Enhanced memory logging with growth tracking
            elif memory_growth > 200:  # 200MB+ growth = high concern
                logging.error(f"HIGH MEMORY GROWTH: {memory_mb:.1f}MB (+{memory_growth:.1f}MB from baseline) - potential system buffer leak")
                memory_growth_warnings = max(0, memory_growth_warnings - 1)  # Decay warnings slowly
            elif memory_growth > 100:  # 100MB+ growth = moderate concern
                logging.warning(f"ELEVATED MEMORY GROWTH: {memory_mb:.1f}MB (+{memory_growth:.1f}MB from baseline) - monitoring system buffers")
                memory_growth_warnings = max(0, memory_growth_warnings - 1)
            elif abs(memory_change) > 25:  # Significant change (25MB) - use memory_change not last_memory_mb
                logging.info(f"MEMORY CHANGE: {memory_mb:.1f}MB (change: {memory_change:+.1f}MB, growth: +{memory_growth:.1f}MB)")
                memory_growth_warnings = max(0, memory_growth_warnings - 1)
            elif memory_mb > 150:  # Regular monitoring for moderate usage
                logging.info(f"Memory: {memory_mb:.1f}MB (+{memory_growth:.1f}MB from baseline)")
                memory_growth_warnings = max(0, memory_growth_warnings - 1)
            else:
                # Reset warnings when memory is stable and low
                memory_growth_warnings = max(0, memory_growth_warnings - 1)
            
            last_memory_mb = memory_mb
            await asyncio.sleep(3)  # Check every 3 seconds for faster detection
        except Exception as e:
            logging.error(f"Memory monitor error: {e}")
            await asyncio.sleep(5)


async def main():
    # Set up logging to both console and file (one file per session)
    import logging.handlers
    import datetime
    import glob
    
    # Create logs directory if it doesn't exist
    import os
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # Clean up old log files, keep only last 5 sessions
    existing_logs = sorted(glob.glob('logs/fermax-onvif-*.log'))
    if len(existing_logs) >= 5:
        # Remove oldest log files, keep 4 (so with new one we'll have 5)
        for old_log in existing_logs[:-4]:
            try:
                os.remove(old_log)
                print(f"Removed old log file: {old_log}")
            except Exception as e:
                print(f"Could not remove old log file {old_log}: {e}")
    
    # Create log file with timestamp for this session
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f'logs/fermax-onvif-{timestamp}.log'
    
    # Set up file logging for this session
    file_handler = logging.FileHandler(log_filename)
    file_handler.setLevel(logging.DEBUG)
    file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(file_formatter)
    
    # Set up console logging
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(console_formatter)
    
    # Configure root logger
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    logging.info("=== FERMAX ONVIF APPLICATION STARTING ===")
    logging.info(f"Session log file: {log_filename}")
    logging.info(f"Memory limit: {config.MEMORY_LIMIT_MB}MB")
    logging.info(f"Hardware acceleration: {'ENABLED (VAAPI)' if config.ENABLE_HARDWARE_ACCELERATION else 'DISABLED (software only)'}")

    # Set up signal handlers for graceful shutdown
    shutdown_event = asyncio.Event()
    
    def signal_handler():
        logging.info("Received shutdown signal, initiating graceful shutdown...")
        shutdown_event.set()
    
    # Register signal handlers
    loop = asyncio.get_running_loop()
    for sig in (signal.SIGTERM, signal.SIGINT):
        loop.add_signal_handler(sig, signal_handler)

    host_ip = get_local_ip()

    try:
        logging.info("Starting MediaMTX server from './mediamtx mediamtx.yml'")
        config.mediamtx_process = await asyncio.create_subprocess_exec(
            './mediamtx', 'mediamtx.yml',
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        asyncio.create_task(log_mediamtx_output(config.mediamtx_process.stdout, logging.info, prefix="[MediaMTX] "))
        asyncio.create_task(log_process_output(config.mediamtx_process.stderr, logging.error, prefix="[MediaMTX-ERROR] "))
        logging.info(f"MediaMTX process started with PID: {config.mediamtx_process.pid}. Waiting a moment for it to initialize...")
        await asyncio.sleep(2)
    except FileNotFoundError:
        logging.error("FATAL: './mediamtx' executable not found. Please ensure it is in the same directory as the script.")
        return

    # Create audio FIFO first
    import tempfile
    import os
    audio_fifo = os.path.join(tempfile.gettempdir(), 'fermax_audio_fifo')
    
    # Clean up any existing FIFO first
    try:
        if os.path.exists(audio_fifo):
            os.unlink(audio_fifo)
    except Exception as e:
        logging.warning(f"Could not remove existing FIFO: {e}")
    
    # Create new FIFO
    try:
        os.mkfifo(audio_fifo)
        logging.info(f"Created audio FIFO at: {audio_fifo}")
    except Exception as e:
        logging.error(f"FATAL: Could not create audio FIFO: {e}")
        return
    
    # Verify FIFO was created successfully
    if not os.path.exists(audio_fifo):
        logging.error("FATAL: Audio FIFO was not created successfully")
        return
    
    # Check if FIFO is accessible
    if not os.access(audio_fifo, os.R_OK | os.W_OK):
        logging.error("FATAL: Audio FIFO is not accessible")
        return
    
    config.audio_fifo_path = audio_fifo
    
    # Start FFmpeg first so it's ready to read from FIFO
    use_software = not config.ENABLE_HARDWARE_ACCELERATION
    if not await start_ffmpeg(use_software_encoder=use_software):
        if config.ENABLE_HARDWARE_ACCELERATION:
            logging.error("Failed to start FFmpeg with hardware encoder, trying software encoder...")
            if not await start_ffmpeg(use_software_encoder=True):
                logging.error("FATAL: Could not start FFmpeg with either hardware or software encoder.")
                return
        else:
            logging.error("FATAL: Could not start FFmpeg with software encoder.")
            return
    
    # Give FFmpeg more time to initialize and be ready to read from FIFO
    await asyncio.sleep(3)
    
    # Wait for MediaMTX to detect the stream before starting generators
    logging.info("Waiting for FFmpeg to establish RTSP connection to MediaMTX...")
    
    # Start generators immediately, they will create the stream that FFmpeg connects to
    pass  # Remove the waiting logic for now

    # Start background tasks
    generator_task = asyncio.create_task(stream_generator())
    audio_generator_task = asyncio.create_task(audio_stream_generator())
    ffmpeg_monitor_task = asyncio.create_task(ffmpeg_monitor())
    memory_monitor_task = asyncio.create_task(memory_monitor())
    
    # Add task names for better debugging
    generator_task.set_name("video_stream_generator")
    audio_generator_task.set_name("audio_stream_generator")
    ffmpeg_monitor_task.set_name("ffmpeg_monitor")
    memory_monitor_task.set_name("memory_monitor")

    app = web.Application()
    app.router.add_post('/start', handle_start)
    app.router.add_post('/start_twoway', handle_start_twoway)
    app.router.add_post('/stop', handle_stop)
    app.router.add_post('/onvif/device_service', handle_onvif)
    app.router.add_get('/onvif/snapshot', handle_snapshot)
    app.router.add_post('/press_doorbell', handle_doorbell_press_with_image)
    app.router.add_post('/update_snapshot', handle_update_snapshot)
    app.router.add_post('/audio_input', handle_audio_input)

    runner = web.AppRunner(app)
    await runner.setup()
    site = web.TCPSite(runner, '0.0.0.0', config.ONVIF_PORT)
    await site.start()
    logging.info(f"Web server started on http://{host_ip}:{config.ONVIF_PORT}")

    onvif_task = asyncio.create_task(start_onvif_discovery(host_ip))
    shutdown_task = asyncio.create_task(shutdown_event.wait())

    mediamtx_wait_task = asyncio.create_task(config.mediamtx_process.wait())

    done, pending = await asyncio.wait(
        [generator_task, audio_generator_task, ffmpeg_monitor_task, memory_monitor_task, mediamtx_wait_task, onvif_task, shutdown_task],
        return_when=asyncio.FIRST_COMPLETED
    )
    
    # Log which task completed/failed
    for task in done:
        task_name = getattr(task, '_name', 'unknown_task')
        if task.exception():
            logging.error(f"Critical task '{task_name}' failed with exception: {task.exception()}")
        else:
            logging.info(f"Critical task '{task_name}' completed normally")
    
    for task in pending:
        task_name = getattr(task, '_name', 'unknown_task')
        logging.info(f"Cancelling pending task: {task_name}")
        task.cancel()
    logging.info("A critical process (MediaMTX, Generator, FFmpeg Monitor, or ONVIF) has exited. Shutting down.")
    logging.info("=== FERMAX ONVIF APPLICATION SHUTTING DOWN ===")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logging.info("Process interrupted by user.")
    finally:
        if config.ffmpeg_process and config.ffmpeg_process.returncode is None:
            logging.info("Terminating FFmpeg process...")
            config.ffmpeg_process.terminate()
        if config.mediamtx_process and config.mediamtx_process.returncode is None:
            logging.info("Terminating MediaMTX process...")
            config.mediamtx_process.terminate()
        
        # Clean up audio FIFO
        import os
        if config.audio_fifo_path and os.path.exists(config.audio_fifo_path):
            try:
                os.unlink(config.audio_fifo_path)
                logging.info("Removed audio FIFO")
            except Exception as e:
                logging.warning(f"Could not remove audio FIFO: {e}")
        
        logging.info("Cleanup complete. Exiting.")
        logging.info("=== FERMAX ONVIF APPLICATION ENDED ===")
