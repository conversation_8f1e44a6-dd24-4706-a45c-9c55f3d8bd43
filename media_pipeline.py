import asyncio
import logging
import numpy as np
import struct
from PIL import Image

import config
from config import (
    STILL_IMAGE_PATH, VIDEO_WIDTH, VIDEO_HEIGHT, DUMMY_FPS
)


async def audio_stream_generator():
    """Generate audio stream for FFmpeg - live audio when available, silence otherwise"""
    logging.info("Starting audio stream generator...")

    try:
        while True:
            # Check if audio_fifo_path is available
            if not hasattr(config, 'audio_fifo_path') or config.audio_fifo_path is None:
                await asyncio.sleep(1)
                continue

            # Verify FIFO actually exists
            import os
            if not os.path.exists(config.audio_fifo_path):
                await asyncio.sleep(1)
                continue

            break
    except Exception as e:
        logging.error(f"Audio stream generator initialization error: {e}", exc_info=True)
        return

    # Generate silence frames for fallback (8kHz, mono, 16-bit)
    sample_rate = 8000
    frame_duration = 0.02  # 20ms frames
    samples_per_frame = int(sample_rate * frame_duration)
    silence_frame = struct.pack('<' + 'h' * samples_per_frame, *([0] * samples_per_frame))
    
    # Audio debug tracking
    audio_bytes_written = 0
    audio_last_log_time = 0
    
    # Nothing to initialize for balanced approach
    
    # Main retry loop - restart when FIFO connection is broken
    while True:
        try:
            # Wait for FFmpeg to be ready to read from FIFO
            retry_count = 0
            max_retries = 10
            fifo_opened = False
            
            while retry_count < max_retries:
                try:
                    import os
                    import time
                    
                    # Wait a bit before each attempt to let FFmpeg initialize
                    if retry_count > 0:
                        await asyncio.sleep(1)
                    
                    # Try non-blocking first to see if someone is reading
                    try:
                        fifo_fd = os.open(config.audio_fifo_path, os.O_WRONLY | os.O_NONBLOCK)
                        # Keep it in non-blocking mode to prevent deadlocks
                        import fcntl
                        
                        with os.fdopen(fifo_fd, 'wb') as audio_fifo:
                            fifo_opened = True
                            silence_frame_count = 0
                            while True:
                                try:
                                    silence_frame_count += 1
                                    
                                    # Always run at full performance
                                    audio_frame_delay = frame_duration  # 20ms frames (50 FPS)
                                    
                                    # Detailed audio logging every 10 seconds
                                    import time
                                    current_time = time.time()
                                    if current_time - audio_last_log_time >= 10:
                                        live_queue_size = config.live_audio_queue.qsize() if hasattr(config, 'live_audio_queue') and hasattr(config.live_audio_queue, 'qsize') else 'N/A'
                                        is_audio_live = "LIVE" if config.is_audio_live.is_set() else "SILENCE"
                                        mb_written = audio_bytes_written / (1024 * 1024)
                                        logging.info(f"[AUDIO DEBUG] Frame: {silence_frame_count}, Mode: {is_audio_live}, Queue: {live_queue_size}, Data: {mb_written:.1f}MB")
                                        audio_last_log_time = current_time
                                    
                                    if silence_frame_count % 1250 == 0:  # Log every 25 seconds
                                        logging.info(f"Audio generator: processed {silence_frame_count} silence frames")
                                    if config.is_audio_live.is_set():
                                        # Processing live audio frame (logging removed for performance)
                                        try:
                                            # Get live audio frame from queue
                                            audio_data = await config.live_audio_queue.get()
                                            if audio_data is None:
                                                # Signal to switch back to silence
                                                config.live_audio_queue.task_done()
                                                logging.info("Received audio end signal, switching back to silence")
                                                continue
                                            
                                            # Write live audio data to FIFO (non-blocking)
                                            try:
                                                audio_fifo.write(audio_data)
                                                audio_fifo.flush()
                                                audio_bytes_written += len(audio_data)
                                            except BlockingIOError:
                                                logging.debug(f"[AUDIO DEBUG] FIFO buffer full, skipping live audio frame")
                                                pass
                                            config.live_audio_queue.task_done()
                                            
                                        except Exception as e:
                                            logging.warning(f"Error processing live audio frame: {e}")
                                            # On error, clear live audio flag and continue with silence
                                            config.is_audio_live.clear()
                                            # Flush the audio queue to prevent buildup
                                            try:
                                                while not config.live_audio_queue.empty():
                                                    config.live_audio_queue.get_nowait()
                                                    config.live_audio_queue.task_done()
                                            except:
                                                pass
                                            continue
                                    else:
                                        # Write silence frame with non-blocking approach
                                        try:
                                            # Write audio frame to FIFO
                                            audio_fifo.write(silence_frame)
                                            audio_fifo.flush()
                                            audio_bytes_written += len(silence_frame)
                                            await asyncio.sleep(audio_frame_delay)
                                        except BlockingIOError:
                                            # FIFO buffer is full, skip this frame and continue
                                            # Audio FIFO buffer full, skipping frame
                                            await asyncio.sleep(audio_frame_delay)
                                        except (BrokenPipeError, OSError) as e:
                                            logging.error(f"Error writing silence frame: {e}")
                                            # FIFO is broken, need to restart connection
                                            raise
                                    
                                except Exception as e:
                                    logging.error(f"Error writing to audio FIFO: {e}")
                                    # Connection lost, break to outer loop to retry
                                    raise
                            
                    except (OSError, BrokenPipeError) as e:
                        # FIFO not ready yet, FFmpeg probably hasn't opened it for reading
                        raise  # Re-raise to trigger retry logic
                        
                except (BrokenPipeError, OSError) as e:
                    retry_count += 1
                    if retry_count >= max_retries:
                        logging.error(f"Failed to open audio FIFO after {max_retries} attempts: {e}")
                        break
                except Exception as e:
                    logging.error(f"Unexpected error opening audio FIFO: {e}")
                    break
            
            if not fifo_opened:
                logging.info("Audio FIFO could not be opened - waiting 3s for FFmpeg to restart")
                await asyncio.sleep(3)
            
        except asyncio.CancelledError:
            logging.info("Audio stream generator cancelled")
            break
        except Exception as e:
            logging.error(f"Audio stream generator error: {e}", exc_info=True)
            await asyncio.sleep(3)


async def stream_generator():
    logging.info("Starting stream generator...")
    
    # Load dummy image once
    try:
        dummy_image = Image.open(STILL_IMAGE_PATH).convert("RGB")
        dummy_image = dummy_image.resize((VIDEO_WIDTH, VIDEO_HEIGHT))
        dummy_frame = np.array(dummy_image)
        dummy_frame = dummy_frame[:, :, ::-1]
        dummy_frame_bytes = dummy_frame.tobytes()
    except FileNotFoundError:
        logging.error(f"FATAL: Dummy image not found at '{STILL_IMAGE_PATH}'. Exiting.")
        return

    # Keep track of the current FFmpeg process to detect restarts
    current_ffmpeg = None
    last_was_live = False
    
    frame_count = 0
    bytes_written = 0
    last_log_time = 0
    
    # CRITICAL: System buffer monitoring for real backpressure detection
    import psutil
    process = psutil.Process()
    session_start_memory = process.memory_info().rss / (1024 * 1024)  # MB
    last_memory_check = 0
    memory_growth_threshold = 200  # MB growth indicates buffer accumulation
    backpressure_detected = False
    emergency_throttle = False
    
    while True:
        try:
            frame_count += 1
            
            # Adaptive performance based on system buffer monitoring
            frame_delay = 1 / DUMMY_FPS  # 24 FPS base rate
            
            if emergency_throttle:
                frame_delay *= 4  # 75% slowdown during emergency
                # Skip additional frames during emergency
                if frame_count % 4 != 0:  # Skip 3 out of 4 frames
                    frame_count += 1
                    await asyncio.sleep(frame_delay)
                    continue
            elif backpressure_detected:
                frame_delay *= 2  # 50% slowdown during backpressure
            
            # CRITICAL: System memory monitoring for real backpressure detection
            import time
            current_time = time.time()
            
            if current_time - last_log_time >= 10:
                queue_size = config.live_frame_queue.qsize() if hasattr(config.live_frame_queue, 'qsize') else 'unknown'
                is_live_status = "LIVE" if config.is_live.is_set() else "DUMMY"
                mb_written = bytes_written / (1024 * 1024)
                
                # CRITICAL: Monitor actual process memory growth
                current_memory = process.memory_info().rss / (1024 * 1024)  # MB
                memory_growth = current_memory - session_start_memory
                
                # Detect system buffer accumulation via memory growth
                if memory_growth > memory_growth_threshold:
                    if not backpressure_detected:
                        backpressure_detected = True
                        logging.critical(f"[SYSTEM BUFFER] Memory growth detected: {memory_growth:.1f}MB indicates buffer accumulation")
                    
                    # Emergency throttling if memory growth is severe
                    if memory_growth > 500:  # 500MB+ growth = emergency
                        if not emergency_throttle:
                            emergency_throttle = True
                            logging.critical(f"[EMERGENCY] Severe memory growth {memory_growth:.1f}MB - activating emergency throttling")
                elif memory_growth < 100 and (backpressure_detected or emergency_throttle):
                    backpressure_detected = False
                    emergency_throttle = False
                    logging.info(f"[SYSTEM BUFFER] Memory normalized: {memory_growth:.1f}MB growth")
                
                logging.info(f"[STREAM DEBUG] Frame: {frame_count}, Mode: {is_live_status}, Queue: {queue_size}, Memory: {current_memory:.1f}MB (+{memory_growth:.1f}MB), Backpressure: {backpressure_detected}, Emergency: {emergency_throttle}, FFmpeg PID: {config.ffmpeg_process.pid if config.ffmpeg_process else 'None'}")
                    
                last_log_time = current_time
                
            if frame_count % 1200 == 0:  # Log every 50 seconds
                logging.info(f"Stream generator: processed {frame_count} frames")
            
            # Check if FFmpeg process has changed (restarted)
            if config.ffmpeg_process != current_ffmpeg:
                if config.ffmpeg_process is None:
                    logging.info("Waiting for FFmpeg process to be available...")
                    await asyncio.sleep(1)
                    continue
                else:
                    logging.info(f"FFmpeg process changed/restarted (PID: {config.ffmpeg_process.pid}), reconnecting stream generator...")
                    current_ffmpeg = config.ffmpeg_process
                    # Add small delay to let FFmpeg initialize
                    await asyncio.sleep(0.5)
            
            # Additional safety check in case ffmpeg_process becomes None during execution
            if config.ffmpeg_process is None or config.ffmpeg_process.stdin is None:
                logging.warning("FFmpeg process or stdin is None. Waiting for restart...")
                current_ffmpeg = None
                await asyncio.sleep(1)
                continue
                
            if config.is_live.is_set():
                if not last_was_live:
                    logging.info("Stream generator: Switched to live video frames")
                    last_was_live = True
                try:
                    # Check if FFmpeg process is still alive before writing
                    if config.ffmpeg_process.returncode is not None:
                        logging.error("FFmpeg process died while processing live frames, forcing reconnection")
                        current_ffmpeg = None
                        await asyncio.sleep(0.1)
                        continue
                    
                    # Use timeout to avoid getting stuck
                    frame_bytes = await asyncio.wait_for(config.live_frame_queue.get(), timeout=1.0)
                    if frame_bytes is None:
                        # Signal to switch back to dummy frames
                        config.live_frame_queue.task_done()
                        logging.info("Received end signal, switching back to dummy frames")
                        continue
                    frame_size = len(frame_bytes)
                    
                    # CRITICAL: Write live frame with hard backpressure detection
                    try:
                        # Use non-blocking write to detect buffer state immediately
                        import fcntl
                        import os
                        
                        fd = config.ffmpeg_process.stdin.fileno()
                        flags = fcntl.fcntl(fd, fcntl.F_GETFL)
                        fcntl.fcntl(fd, fcntl.F_SETFL, flags | os.O_NONBLOCK)
                        
                        try:
                            config.ffmpeg_process.stdin.write(frame_bytes)
                            bytes_written += frame_size
                        except BlockingIOError:
                            logging.critical(f"[PIPE BUFFER] FFmpeg stdin full - dropping live frame and activating emergency mode")
                            
                            # Immediately activate emergency throttling
                            if not emergency_throttle:
                                emergency_throttle = True
                                logging.critical(f"[EMERGENCY] Live frame write blocked - activating emergency throttling")
                            
                            # Aggressively flush live queue to prevent memory buildup
                            flushed_count = 0
                            while not config.live_frame_queue.empty() and flushed_count < 10:
                                try:
                                    dropped_frame = config.live_frame_queue.get_nowait()
                                    config.live_frame_queue.task_done()
                                    del dropped_frame
                                    flushed_count += 1
                                except:
                                    break
                            if flushed_count > 0:
                                logging.critical(f"[EMERGENCY] Flushed {flushed_count} live frames from queue")
                        finally:
                            fcntl.fcntl(fd, fcntl.F_SETFL, flags)
                            
                    except (BrokenPipeError, OSError) as e:
                        logging.critical(f"[PIPE ERROR] Live frame pipe error: {e}")
                        current_ffmpeg = None
                        await asyncio.sleep(0.1)
                        continue
                        
                    config.live_frame_queue.task_done()
                except asyncio.TimeoutError:
                    # No frame available, write dummy frame to keep stream alive
                    try:
                        # CRITICAL: Write timeout fallback with hard backpressure detection
                        frame_size = len(dummy_frame_bytes)
                        
                        try:
                            import fcntl
                            import os
                            
                            fd = config.ffmpeg_process.stdin.fileno()
                            flags = fcntl.fcntl(fd, fcntl.F_GETFL)
                            fcntl.fcntl(fd, fcntl.F_SETFL, flags | os.O_NONBLOCK)
                            
                            try:
                                config.ffmpeg_process.stdin.write(dummy_frame_bytes)
                                bytes_written += frame_size
                            except BlockingIOError:
                                logging.critical(f"[PIPE BUFFER] Timeout fallback blocked - system buffer accumulation detected")
                                if not emergency_throttle:
                                    emergency_throttle = True
                                    logging.critical(f"[EMERGENCY] Timeout fallback blocked - activating emergency throttling")
                            finally:
                                fcntl.fcntl(fd, fcntl.F_SETFL, flags)
                                
                        except (BrokenPipeError, OSError) as e:
                            logging.critical(f"[PIPE ERROR] Timeout fallback pipe error: {e}")
                            current_ffmpeg = None
                            await asyncio.sleep(0.1)
                            continue
                            
                        await asyncio.sleep(frame_delay)
                    except (BrokenPipeError, ConnectionResetError) as e:
                        logging.warning(f"FFmpeg stdin broken while writing dummy frame: {e}")
                        current_ffmpeg = None  # Force reconnection
                        await asyncio.sleep(0.1)
                        continue
                except Exception as e:
                    logging.warning(f"Error processing live frame: {e}")
                    # On error, clear live flag and continue with dummy frames
                    config.is_live.clear()
                    # Flush the queue to prevent buildup
                    try:
                        while not config.live_frame_queue.empty():
                            config.live_frame_queue.get_nowait()
                            config.live_frame_queue.task_done()
                    except:
                        pass
                    continue
            else:
                if last_was_live:
                    logging.info("Stream generator: Switched to dummy video frames")
                    last_was_live = False
                try:
                    # Check if FFmpeg process is still alive before writing
                    if config.ffmpeg_process.returncode is not None:
                        logging.error("FFmpeg process died, forcing reconnection")
                        current_ffmpeg = None
                        await asyncio.sleep(0.1)
                        continue
                    
                    # CRITICAL: Write frame to FFmpeg with backpressure detection
                    frame_size = len(dummy_frame_bytes)
                    
                    # Measure write time to detect stdin blocking/backpressure
                    import time
                    write_start = time.time()
                    try:
                        config.ffmpeg_process.stdin.write(dummy_frame_bytes)
                        write_duration = time.time() - write_start
                        
                        # Detect backpressure (slow writes indicate buffer overflow)
                        if write_duration > 0.1:  # 100ms threshold
                            logging.warning(f"[BACKPRESSURE] Slow stdin write: {write_duration*1000:.1f}ms - FFmpeg input buffer overflow detected")
                            
                            # CRITICAL: Skip next few frames to reduce pressure
                            skip_frames = min(int(write_duration * DUMMY_FPS), 10)  # Skip proportional frames, max 10
                            if skip_frames > 0:
                                logging.warning(f"[BACKPRESSURE] Skipping {skip_frames} frames to relieve buffer pressure")
                                for _ in range(skip_frames):
                                    frame_count += 1
                                    await asyncio.sleep(frame_delay)
                        
                        bytes_written += frame_size
                        if frame_count % 240 == 0:  # Log every 10 seconds at 24fps
                            logging.info(f"[STREAM DEBUG] Dummy frame written: {frame_size} bytes, total: {bytes_written/(1024*1024):.1f}MB, write_time: {write_duration*1000:.1f}ms")
                            
                    except BlockingIOError:
                        # Stdin buffer is full - this is the key indicator of backpressure
                        logging.critical(f"[BACKPRESSURE] FFmpeg stdin buffer full - dropping frame to prevent memory explosion")
                        # Don't write the frame, just continue
                    
                    # Frame written successfully
                    await asyncio.sleep(frame_delay)
                except (BrokenPipeError, ConnectionResetError) as e:
                    logging.error(f"FFmpeg stdin broken while writing dummy frames: {e}")
                    logging.info("This suggests FFmpeg process died - monitor should restart it")
                    current_ffmpeg = None  # Force reconnection check
                    await asyncio.sleep(0.1)
                    continue
        except asyncio.CancelledError:
            logging.info("Video stream generator cancelled")
            break
        except (BrokenPipeError, ConnectionResetError) as e:
            logging.error(f"[STREAM DEBUG] FFmpeg process pipe broken: {e}. Total data written: {bytes_written/(1024*1024):.1f}MB")
            break
        except Exception as e:
            logging.error(f"[STREAM DEBUG] Error in stream_generator: {e}. Total data written: {bytes_written/(1024*1024):.1f}MB", exc_info=True)
            break